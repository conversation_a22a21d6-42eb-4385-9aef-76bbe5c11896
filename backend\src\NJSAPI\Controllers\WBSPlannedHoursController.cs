using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NJS.Infrastructure.Data;
using NJS.Domain.Entities;
using NJS.Application.Dtos;

namespace NJSAPI.Controllers
{
    [ApiController]
    [Route("api/wbs/tasks/{taskId}/planned-hours")]
    public class WBSPlannedHoursController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public WBSPlannedHoursController(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// Get planned hours for a specific WBS task
        /// </summary>
        /// <param name="taskId">WBS Task ID</param>
        /// <returns>List of planned hours</returns>
        [HttpGet]
        public async Task<IActionResult> GetPlannedHours(int taskId)
        {
            var task = await _context.WBSTasks
                .Include(t => t.PlannedHours)
                .FirstOrDefaultAsync(t => t.Id == taskId && !t.IsDeleted);

            if (task == null)
            {
                return NotFound($"WBS Task with ID {taskId} not found");
            }

            var plannedHours = task.PlannedHours.Select(ph => new PlannedHourDto
            {
                Year = int.TryParse(ph.Year, out var year) ? year : DateTime.Now.Year,
                MonthNo = ph.Month,
                Day = ph.Day?.ToString(),
                WeekNo = ph.WeekNumber,
                PlannedHours = ph.PlannedHours
            }).ToList();

            return Ok(plannedHours);
        }

        /// <summary>
        /// Add planned hours for a WBS task
        /// </summary>
        /// <param name="taskId">WBS Task ID</param>
        /// <param name="plannedHourDto">Planned hour data</param>
        /// <returns>Created planned hour</returns>
        [HttpPost]
        public async Task<IActionResult> AddPlannedHours(int taskId, [FromBody] PlannedHourDto plannedHourDto)
        {
            // Validate planned hours not exceeding 24 hours per day
            if (plannedHourDto.PlannedHours > 24)
            {
                return BadRequest("Planned hours cannot exceed 24 hours per day");
            }

            var task = await _context.WBSTasks
                .Include(t => t.PlannedHours)
                .Include(t => t.WorkBreakdownStructure)
                .FirstOrDefaultAsync(t => t.Id == taskId && !t.IsDeleted);

            if (task == null)
            {
                return NotFound($"WBS Task with ID {taskId} not found");
            }

            // Get or create planned hour header
            var header = await GetOrCreatePlannedHourHeader(task.WorkBreakdownStructure.ProjectId, task.TaskType);

            var plannedHour = new WBSTaskPlannedHour
            {
                WBSTaskId = taskId,
                WBSTaskPlannedHourHeaderId = header.Id,
                Year = plannedHourDto.Year.ToString(),
                Month = plannedHourDto.MonthNo,
                Day = string.IsNullOrEmpty(plannedHourDto.Day) ? null : int.Parse(plannedHourDto.Day),
                WeekNumber = plannedHourDto.WeekNo,
                PlannedHours = plannedHourDto.PlannedHours,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System" // Replace with current user
            };

            _context.WBSTaskPlannedHours.Add(plannedHour);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetPlannedHours), new { taskId }, plannedHourDto);
        }

        /// <summary>
        /// Update planned hours for a WBS task
        /// </summary>
        /// <param name="taskId">WBS Task ID</param>
        /// <param name="plannedHourId">Planned Hour ID</param>
        /// <param name="plannedHourDto">Updated planned hour data</param>
        /// <returns>Updated planned hour</returns>
        [HttpPut("{plannedHourId}")]
        public async Task<IActionResult> UpdatePlannedHours(int taskId, int plannedHourId, [FromBody] PlannedHourDto plannedHourDto)
        {
            // Validate planned hours not exceeding 24 hours per day
            if (plannedHourDto.PlannedHours > 24)
            {
                return BadRequest("Planned hours cannot exceed 24 hours per day");
            }

            var plannedHour = await _context.WBSTaskPlannedHours
                .FirstOrDefaultAsync(ph => ph.Id == plannedHourId && ph.WBSTaskId == taskId);

            if (plannedHour == null)
            {
                return NotFound($"Planned hour with ID {plannedHourId} not found for task {taskId}");
            }

            plannedHour.Year = plannedHourDto.Year.ToString();
            plannedHour.Month = plannedHourDto.MonthNo;
            plannedHour.Day = string.IsNullOrEmpty(plannedHourDto.Day) ? null : int.Parse(plannedHourDto.Day);
            plannedHour.WeekNumber = plannedHourDto.WeekNo;
            plannedHour.PlannedHours = plannedHourDto.PlannedHours;
            plannedHour.UpdatedAt = DateTime.UtcNow;
            plannedHour.UpdatedBy = "System"; // Replace with current user

            await _context.SaveChangesAsync();

            return Ok(plannedHourDto);
        }

        /// <summary>
        /// Delete planned hours for a WBS task
        /// </summary>
        /// <param name="taskId">WBS Task ID</param>
        /// <param name="plannedHourId">Planned Hour ID</param>
        /// <returns>Success or error response</returns>
        [HttpDelete("{plannedHourId}")]
        public async Task<IActionResult> DeletePlannedHours(int taskId, int plannedHourId)
        {
            var plannedHour = await _context.WBSTaskPlannedHours
                .FirstOrDefaultAsync(ph => ph.Id == plannedHourId && ph.WBSTaskId == taskId);

            if (plannedHour == null)
            {
                return BadRequest($"Invalid planned hour ID {plannedHourId} for task {taskId}");
            }

            _context.WBSTaskPlannedHours.Remove(plannedHour);
            await _context.SaveChangesAsync();

            return Ok(new { message = "Planned hour deleted successfully" });
        }

        private async Task<WBSTaskPlannedHourHeader> GetOrCreatePlannedHourHeader(int projectId, TaskType taskType)
        {
            var header = await _context.WBSTaskPlannedHourHeaders
                .FirstOrDefaultAsync(h => h.ProjectId == projectId && h.TaskType == taskType);

            if (header == null)
            {
                header = new WBSTaskPlannedHourHeader
                {
                    ProjectId = projectId,
                    TaskType = taskType,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = "System" // Replace with current user
                };
                _context.WBSTaskPlannedHourHeaders.Add(header);
                await _context.SaveChangesAsync();
            }

            return header;
        }
    }
}
