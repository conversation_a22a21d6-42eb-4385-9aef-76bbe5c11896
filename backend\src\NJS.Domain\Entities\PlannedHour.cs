using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NJS.Domain.Entities
{
    /// <summary>
    /// Entity representing planned hours for various tasks and resources
    /// </summary>
    public class PlannedHour
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int WBSTaskId { get; set; }

        [Required]
        public int Year { get; set; }

        [Required]
        [Range(1, 12, ErrorMessage = "Month must be between 1 and 12")]
        public int MonthNo { get; set; }

        [Range(1, 31, ErrorMessage = "Day must be between 1 and 31")]
        public int? Day { get; set; }

        [Range(1, 53, ErrorMessage = "Week number must be between 1 and 53")]
        public int? WeekNo { get; set; }

        [Required]
        [Range(0.1, 24.0, ErrorMessage = "Planned hours must be between 0.1 and 24.0")]
        public double PlannedHours { get; set; }

        [Range(0, 24.0, ErrorMessage = "Actual hours must be between 0 and 24.0")]
        public double? ActualHours { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        [StringLength(50)]
        public string? ResourceType { get; set; } // e.g., "Manpower", "Equipment", "Material"

        [StringLength(450)]
        public string? AssignedUserId { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Required]
        [StringLength(100)]
        public string CreatedBy { get; set; }

        public DateTime? UpdatedAt { get; set; }

        [StringLength(100)]
        public string? UpdatedBy { get; set; }

        // Navigation properties
        [ForeignKey(nameof(WBSTaskId))]
        public WBSTask WBSTask { get; set; }

        [ForeignKey(nameof(AssignedUserId))]
        public User? AssignedUser { get; set; }
    }
}
